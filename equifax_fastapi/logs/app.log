YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error in upload wrapper endpoint: 'coroutine' object has no attribute 'get'
Error in upload wrapper endpoint: 'coroutine' object has no attribute 'get'
Error in upload wrapper endpoint: 'coroutine' object has no attribute 'get'
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error calling YOLO service in process_single_image: YoloService.detect_cards() missing 2 required positional arguments: 'file_id' and 'file_extension'
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error calling YOLO service in process_single_image: YoloService.detect_cards() missing 1 required positional argument: 'file_extension'
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error in upload wrapper endpoint: 'YoloResponse' object has no attribute 'get'
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error in upload wrapper endpoint: 'YoloResponse' object has no attribute 'get'
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
Error in upload wrapper endpoint: 'YoloResponse' object has no attribute 'get'
