Failed to load YOLO model: cannot import name 'torch_load' from 'ultralytics.utils.patches' (/home/<USER>/anaconda3/envs/test/lib/python3.11/site-packages/ultralytics/utils/patches.py)
Failed to load YOLO model: cannot import name 'torch_load' from 'ultralytics.utils.patches' (/home/<USER>/anaconda3/envs/test/lib/python3.11/site-packages/ultralytics/utils/patches.py)
Failed to load YOLO model: [Errno 2] No such file or directory: 'equifax_fastapi/weights/best.pt'. Cache may be out of date, try `force_reload=True` or see https://docs.ultralytics.com/yolov5/tutorials/pytorch_hub_model_loading for help.
YOLO model loaded successfully using torch.hub
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
FILENAME :f17b4a70-81a5-11f0-8885-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 16:53:37 STATUS:MASKED API:yolo
FILENAME :a800eb92-81a6-11f0-8885-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 16:58:43 STATUS:MASKED API:yolo
FILENAME :3b0d107c-81a8-11f0-8885-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 17:09:59 STATUS:MASKED API:yolo
FILENAME :57205a44-81a8-11f0-8885-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 17:10:46 STATUS:MASKED API:yolo
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
FILENAME :d2c71a4e-81ac-11f0-b0d8-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 17:42:51 STATUS:MASKED API:yolo
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
FILENAME :2efb2184-81ad-11f0-8527-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 17:45:26 STATUS:MASKED API:yolo
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
YOLO model loaded successfully using torch.hub
PaddleOCR loaded successfully
FILENAME :61cfddc0-81ad-11f0-bb9e-78af086dcbb2 FILETYPE :png ISCARD:Yes TIMESTAMP:25-08-2025 17:46:51 STATUS:MASKED API:yolo
