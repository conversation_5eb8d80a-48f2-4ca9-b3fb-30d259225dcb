from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form
import os
from datetime import datetime, timezone
import cv2
import numpy as np
from fastapi.responses import StreamingResponse
import base64
import time
from typing import Dict, Any, List
import io
import uuid

from .services.file_handler import FileHandler
from .core.config import config
from .core.logger import app_logger
from .models.schemas import ProcessFileRequest, FileStatus, Detection
from .utils.process import process_single_image
from .utils.helpers import mask_card_number_boxes

# Initialize FastAPI app
app = FastAPI(
    title="Equifax Card Redaction API",
    description="FastAPI application for card detection and redaction using YOLO and OCR",
    version="1.0.0",
    debug=config.debug
)

# Initialize file handler service
file_handler = FileHandler()

# Directory to store processed output files
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "outputs")
os.makedirs(OUTPUT_DIR, exist_ok=True)

def _save_result_data(result_data, content_type: str, base_name: str, original_extension: str):
    """
    Save processed result to disk inside OUTPUT_DIR.

    - If result_data is bytes: save single file with correct extension.
    - If result_data is list[bytes]: save multiple files with index suffix.
    Returns a list of saved absolute file paths.
    """
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%S%fZ")

    # Determine extension from original extension or content type
    ext_map = {
        "application/pdf": "pdf",
        "image/jpeg": "jpg",
        "image/png": "png",
        "image/heic": "heic",
        "application/json": "json"  # used when returning list of images for DOCX
    }
    ext = original_extension.lower() if original_extension else ext_map.get(content_type, "bin")

    saved_paths = []
    if isinstance(result_data, list):
        # Save each image bytes separately (commonly from DOCX processing)
        for idx, img_bytes in enumerate(result_data):
            filename = f"{base_name}_{timestamp}_{idx}.{ 'jpg' if ext == 'json' else ext }"
            abs_path = os.path.join(OUTPUT_DIR, filename)
            with open(abs_path, "wb") as f:
                f.write(img_bytes)
            saved_paths.append(abs_path)
    else:
        # Save a single file
        filename = f"{base_name}_{timestamp}.{ext}"
        abs_path = os.path.join(OUTPUT_DIR, filename)
        with open(abs_path, "wb") as f:
            f.write(result_data)
        saved_paths.append(abs_path)

    return saved_paths

def _validate_file_data(file_data: bytes, file_type: str) -> tuple[bool, str, str]:
    """
    Validate file data and type with comprehensive checks
    
    Args:
        file_data: Raw file bytes
        file_type: File extension/type
        
    Returns:
        tuple: (is_valid, file_id, error_message)
    """
    # Generate file ID for tracking
    file_id = str(uuid.uuid1())
    
    # Check if file data is empty
    if not file_data or len(file_data) == 0:
        return False, file_id, "File data is empty"
    
    # Check file size limits
    max_size = config.max_file_size if hasattr(config, 'max_file_size') else 50 * 1024 * 1024  # 50MB default
    if len(file_data) > max_size:
        return False, file_id, f"File size exceeds maximum limit of {max_size / (1024*1024):.1f}MB"
    
    # Validate file type using file handler
    validation_result = file_handler.validate_file_type(file_type)
    if not validation_result.is_valid:
        return False, file_id, validation_result.error_message or f"Unsupported file type: {file_type}"
    
    return True, file_id, ""

def _validate_and_convert_file(file_data: bytes, file_type: str) -> tuple[bool, Any, str]:
    """
    Validate and convert file to image arrays
    
    Args:
        file_data: Raw file bytes
        file_type: File extension/type
        
    Returns:
        tuple: (success, conversion_result, error_message)
    """
    # First validate the file
    is_valid, file_id, error_message = _validate_file_data(file_data, file_type)
    if not is_valid:
        return False, None, error_message
    
    # Convert file to image arrays
    conversion_result = file_handler.convert_file_to_images(file_data, file_type)
    if not conversion_result.success:
        return False, None, conversion_result.error_message
    
    return True, conversion_result, ""

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Equifax Card Redaction API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "services": {
            "yolo": "loaded",
            "ocr": "loaded",
            "file_handler": "ready"
        }
    }

@app.post("/wrapper", response_model=Dict[str, Any])
async def wrapper_endpoint(request: ProcessFileRequest):
    """
    Main wrapper endpoint that follows the refined architecture:
    1. Validate + convert file
    2. Call YOLO route → get results
    3. Mask based on results
    4. If (cards > card_number), call OCR route → refine results
    5. Apply final masking
    6. Stitch back to original format
    7. Call logging functions
    """
    try:
        start_time = time.time()
        
        # Step 1: Validate + convert file
        file_data = base64.b64decode(request.file_data)
        
        # Use centralized validation function
        is_valid, conversion_result, error_message = _validate_and_convert_file(file_data, request.file_type)
        if not is_valid:
            app_logger.log_api_call(
                filename="Validation failed",
                file_type=request.file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "File read error"
            }
        
        # Step 2-5: Process each image through the pipeline
        processed_images = []
        overall_status = FileStatus.UNMASKED
        global_mask_count = 0
        global_nocard_count = 0
        
        for page_num, image_array in enumerate(conversion_result.images):
            # Process single image through the pipeline to get raw results
            original_image, yolo_result, ocr_result = await process_single_image(
                image_array.data, 
                conversion_result.file_id, 
                request.file_type,
                page_num if len(conversion_result.images) > 1 else None
            )
            
            current_image = original_image.copy()
            current_status = FileStatus.UNMASKED

            # Log YOLO result
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=request.file_type,
                is_card="Yes" if yolo_result.get("STATUS") != FileStatus.NOT_A_CARD.value else "No",
                status=yolo_result.get("STATUS", "Unknown"),
                api="yolo",
                page_no=page_num
            )
            
            # Apply initial masking based on YOLO results
            if yolo_result.get("detections"):
                detections = []
                for det in yolo_result.get("detections", []):
                    if isinstance(det, dict):
                        detections.append(Detection(**det))
                    # Assuming Detection objects are already handled by the service
                    elif isinstance(det, Detection):
                        detections.append(det)
                
                if detections:
                    current_image = mask_card_number_boxes(current_image, detections)
                    current_status = FileStatus(yolo_result.get("STATUS", FileStatus.UNMASKED.value))

            # If OCR result is available, apply its masking and update status
            if ocr_result.get("processed_image"):
                try:
                    processed_image_bytes = base64.b64decode(ocr_result["processed_image"])
                    processed_image_array = cv2.imdecode(np.frombuffer(processed_image_bytes, np.uint8), cv2.IMREAD_COLOR)
                    
                    if len(processed_image_array.shape) == 3:
                        processed_image_array = cv2.cvtColor(processed_image_array, cv2.COLOR_BGR2RGB)
                    
                    current_image = processed_image_array
                    current_status = FileStatus(ocr_result.get("STATUS", FileStatus.UNMASKED.value))

                    # Log OCR result
                    app_logger.log_api_call(
                        filename=conversion_result.file_id,
                        file_type=request.file_type,
                        is_card="Yes",
                        status=ocr_result.get("STATUS", "Unknown"),
                        api="OCR",
                        page_no=page_num
                    )
                except Exception as e:
                    app_logger.log_error(f"Error decoding OCR processed image in wrapper: {str(e)}")
            elif yolo_result.get("activationstatus") == 1 and yolo_result.get("det_ocr"):
                # If OCR was supposed to run but failed or didn't return processed_image, log it
                app_logger.log_error(f"OCR processing failed or returned no image for page {page_num} (file_id: {conversion_result.file_id})")
            
            # Update counters and overall status based on current_status
            if current_status == FileStatus.MASKED:
                global_mask_count += 1
                overall_status = FileStatus.MASKED
            elif current_status == FileStatus.NOT_A_CARD:
                global_nocard_count += 1
            
            processed_images.append(current_image)
        
        # Determine final status based on original logic
        if global_nocard_count == len(conversion_result.images):
            final_status = FileStatus.NOT_A_CARD
        elif global_mask_count > 0:
            final_status = FileStatus.MASKED
        else:
            final_status = FileStatus.UNMASKED
        
        # Check for timeout
        processing_time = time.time() - start_time
        if processing_time > config.api_timeout:
            final_status = FileStatus.INTERNAL_ERROR
        
        # Step 6: Stitch back to original format
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            app_logger.log_error(f"Error combining processed images: {result_data}")
            return {
                "img-str": request.file_data,
                "img-type": request.file_type,
                "status": "Internal error"
            }
        
        # Encode result as base64
        if isinstance(result_data, list):
            result_base64 = [base64.b64encode(img_bytes).decode('utf-8') for img_bytes in result_data]
        else:
            result_base64 = base64.b64encode(result_data).decode('utf-8')
        
        return {
            "img-str": result_base64,
            "img-type": request.file_type,
            "status": final_status.value
        }
        
    except Exception as e:
        app_logger.log_error(f"Error in wrapper endpoint", e)
        return {
            "img-str": request.file_data,
            "img-type": request.file_type,
            "status": "Internal error"
        }

@app.post("/wrapper/stream")
async def wrapper_stream_endpoint(request: ProcessFileRequest):
    """
    Wrapper endpoint with streaming response
    Returns processed file as streaming response
    """
    try:
        # Decode and validate
        file_data = base64.b64decode(request.file_data)
        
        # Use centralized validation function
        is_valid, conversion_result, error_message = _validate_and_convert_file(file_data, request.file_type)
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)
        
        # Process through pipeline
        processed_images = []
        for page_num, image_array in enumerate(conversion_result.images):
            original_image, yolo_result, ocr_result = process_single_image(
                image_array.data, 
                conversion_result.file_id, 
                request.file_type,
                page_num if len(conversion_result.images) > 1 else None
            )

            current_image = original_image.copy()
            
            # Apply initial masking based on YOLO results
            if yolo_result.get("detections"):
                detections = []
                for det in yolo_result.get("detections", []):
                    if isinstance(det, dict):
                        detections.append(Detection(**det))
                    elif isinstance(det, Detection):
                        detections.append(det)
                
                if detections:
                    current_image = mask_card_number_boxes(current_image, detections)

            # If OCR result is available, apply its masking
            if ocr_result.get("processed_image"):
                try:
                    processed_image_bytes = base64.b64decode(ocr_result["processed_image"])
                    processed_image_array = cv2.imdecode(np.frombuffer(processed_image_bytes, np.uint8), cv2.IMREAD_COLOR)
                    
                    if len(processed_image_array.shape) == 3:
                        processed_image_array = cv2.cvtColor(processed_image_array, cv2.COLOR_BGR2RGB)
                    
                    current_image = processed_image_array
                except Exception as e:
                    app_logger.log_error(f"Error decoding OCR processed image in stream wrapper: {str(e)}")
            
            processed_images.append(current_image)
        
        # Combine and stream
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, request.file_type
        )
        
        if not success:
            raise HTTPException(status_code=500, detail=f"Error processing: {result_data}")
        
        if isinstance(result_data, bytes):
            # Persist the processed output to disk for auditing/debugging
            _save_result_data(result_data, content_type, base_name="processed_stream", original_extension=request.file_type)

            return StreamingResponse(
                io.BytesIO(result_data),
                media_type=content_type,
                headers={"Content-Disposition": f"attachment; filename=processed.{request.file_type}"}
            )
        else:
            raise HTTPException(status_code=500, detail="Unexpected result format")
            
    except HTTPException:
        raise
    except Exception as e:
        app_logger.log_error(f"Error in wrapper streaming endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/wrapper/upload", response_model=Dict[str, Any])
async def wrapper_upload_endpoint(file: UploadFile = File(...), file_type: str = Form(...)):
    """
    Upload endpoint using multipart/form-data. Reads file bytes and reuses the
    same refined pipeline as the base64 endpoint.
    """
    try:
        start_time = time.time()

        # Read file bytes
        file_bytes = await file.read()

        # Use centralized validation function
        is_valid, conversion_result, error_message = _validate_and_convert_file(file_bytes, file_type)
        if not is_valid:
            app_logger.log_api_call(
                filename="Validation failed",
                file_type=file_type,
                is_card="No",
                status="File read error",
                api="File read error"
            )
            raise HTTPException(status_code=400, detail=error_message)

        # Process each image through the pipeline
        processed_images = []
        overall_status = FileStatus.UNMASKED
        global_mask_count = 0
        global_nocard_count = 0

        for page_num, image_array in enumerate(conversion_result.images):
            # Process single image through the pipeline to get raw results
            original_image, yolo_result, ocr_result = process_single_image(
                image_array.data, 
                conversion_result.file_id, 
                file_type,
                page_num if len(conversion_result.images) > 1 else None
            )
            
            current_image = original_image.copy()
            current_status = FileStatus.UNMASKED

            # Log YOLO result
            app_logger.log_api_call(
                filename=conversion_result.file_id,
                file_type=file_type,
                is_card="Yes" if yolo_result.get("STATUS") != FileStatus.NOT_A_CARD.value else "No",
                status=yolo_result.get("STATUS", "Unknown"),
                api="yolo",
                page_no=page_num
            )
            
            # Apply initial masking based on YOLO results
            if yolo_result.get("detections"):
                detections = []
                for det in yolo_result.get("detections", []):
                    if isinstance(det, dict):
                        detections.append(Detection(**det))
                    elif isinstance(det, Detection):
                        detections.append(det)
                
                if detections:
                    current_image = mask_card_number_boxes(current_image, detections)
                    current_status = FileStatus(yolo_result.get("STATUS", FileStatus.UNMASKED.value))

            # If OCR result is available, apply its masking and update status
            if ocr_result.get("processed_image"):
                try:
                    processed_image_bytes = base64.b64decode(ocr_result["processed_image"])
                    processed_image_array = cv2.imdecode(np.frombuffer(processed_image_bytes, np.uint8), cv2.IMREAD_COLOR)
                    
                    if len(processed_image_array.shape) == 3:
                        processed_image_array = cv2.cvtColor(processed_image_array, cv2.COLOR_BGR2RGB)
                    
                    current_image = processed_image_array
                    current_status = FileStatus(ocr_result.get("STATUS", FileStatus.UNMASKED.value))

                    # Log OCR result
                    app_logger.log_api_call(
                        filename=conversion_result.file_id,
                        file_type=file_type,
                        is_card="Yes",
                        status=ocr_result.get("STATUS", "Unknown"),
                        api="OCR",
                        page_no=page_num
                    )
                except Exception as e:
                    app_logger.log_error(f"Error decoding OCR processed image in upload wrapper: {str(e)}")
            elif yolo_result.get("activationstatus") == 1 and yolo_result.get("det_ocr"):
                # If OCR was supposed to run but failed or didn't return processed_image, log it
                app_logger.log_error(f"OCR processing failed or returned no image for page {page_num} (file_id: {conversion_result.file_id})")
            
            # Update counters and overall status based on current_status
            if current_status == FileStatus.MASKED:
                global_mask_count += 1
                overall_status = FileStatus.MASKED
            elif current_status == FileStatus.NOT_A_CARD:
                global_nocard_count += 1

            processed_images.append(current_image)

        # Determine final status based on original logic
        if global_nocard_count == len(conversion_result.images):
            final_status = FileStatus.NOT_A_CARD
        elif global_mask_count > 0:
            final_status = FileStatus.MASKED
        else:
            final_status = FileStatus.UNMASKED

        # Check for timeout
        processing_time = time.time() - start_time
        if processing_time > config.api_timeout:
            final_status = FileStatus.INTERNAL_ERROR

        # Combine processed images back to original format
        success, result_data, content_type = file_handler.combine_processed_images_to_bytes(
            processed_images, file_type
        )

        if not success:
            app_logger.log_error(f"Error combining processed images: {result_data}")
            raise HTTPException(status_code=500, detail="Internal error")

        # Persist the processed output to disk for auditing/debugging
        saved_paths = _save_result_data(result_data, content_type, base_name=conversion_result.file_id, original_extension=file_type)

        # Encode result as base64 for consistency with existing response
        if isinstance(result_data, list):
            result_base64 = [base64.b64encode(img_bytes).decode('utf-8') for img_bytes in result_data]
        else:
            result_base64 = base64.b64encode(result_data).decode('utf-8')

        return {
            "img-str": result_base64,
            "img-type": file_type,
            "status": final_status.value,
            "output_paths": saved_paths  # absolute file path(s) on server
        }

    except HTTPException:
        raise
    except Exception as e:
        app_logger.log_error(f"Error in upload wrapper endpoint", e)
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.api_host,
        port=config.api_port,
        reload=config.debug
    )
