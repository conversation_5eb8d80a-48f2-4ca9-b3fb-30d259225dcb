import os
import torch
import numpy as np
import cv2
from typing import List, <PERSON><PERSON>
from PIL import Image
import io

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import Detection, YoloResponse, FileStatus

class YoloService:
    """YOLO model service for card detection using torch.hub"""

    def __init__(self):
        self.model = None
        self._load_model()

    def _load_model(self):
        """Load YOLO model using torch.hub - clean and simple"""
        try:
            # Set CUDA device (force CPU for consistency)
            os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

            # Load custom YOLOv5 model using torch.hub
            self.model = torch.hub.load(
                'ultralytics/yolov5',
                'custom',
                path=config.yolo_weights,
                trust_repo=True
            )

            # Set model parameters
            self.model.conf = config.conf_thres  # confidence threshold
            self.model.iou = config.iou_thres    # IoU threshold
            self.model.max_det = config.max_det  # maximum detections

            app_logger.log_info("YOLO model loaded successfully using torch.hub")

        except Exception as e:
            app_logger.log_error("Failed to load YOLO model", e)
            raise
    
    def detect_cards(self, image_array: np.ndarray, filename: str, file_id: str,
                    file_extension: str) -> YoloResponse:
        """Detect cards in image using YOLO"""
        try:
            # Convert numpy array to PIL Image for YOLO inference
            if image_array.dtype != np.uint8:
                image_array = (image_array * 255).astype(np.uint8)

            pil_image = Image.fromarray(image_array)

            # Run inference
            results = self.model(pil_image)

            # Process results
            return self._process_results(results, filename, file_id, file_extension)

        except Exception as e:
            app_logger.log_error(f"Error in YOLO detection for {filename}", e)
            return YoloResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus.INTERNAL_ERROR,
                random_id=file_id,
                main_ext=file_extension,
                detections=[],
                activation_status=0
            )
    
    def _process_results(self, results, filename: str, file_id: str,
                        file_extension: str) -> YoloResponse:
        """Process YOLO results and apply threshold logic"""

        detections = []
        cards = []
        card_numbers = []
        no_cards = []

        # Extract detection data from results
        if len(results.xyxy[0]) > 0:
            for detection in results.xyxy[0]:
                x1, y1, x2, y2, conf, cls = detection.tolist()
                class_id = int(cls)
                confidence = float(conf)

                # Map class names (assuming same as original: 0=card, 1=card_number, 2=no_card)
                class_names = {0: 'card', 1: 'card_number', 2: 'no_card'}
                class_name = class_names.get(class_id, f'class_{class_id}')

                det = Detection(
                    x1=x1,
                    y1=y1,
                    x2=x2,
                    y2=y2,
                    confidence=confidence,
                    class_id=class_id,
                    class_name=class_name
                )
                detections.append(det)

                # Apply threshold logic from original code
                if class_id == 0 and confidence >= config.card_threshold:  # card
                    cards.append(det)
                elif class_id == 1 and confidence >= config.card_number_threshold:  # card_number
                    card_numbers.append(det)
                elif class_id == 2:  # no_card
                    no_cards.append(det)

        # Sort by confidence (descending)
        cards = sorted(cards, key=lambda x: x.confidence, reverse=True)
        card_numbers = sorted(card_numbers, key=lambda x: x.confidence, reverse=True)
        no_cards = sorted(no_cards, key=lambda x: x.confidence, reverse=True)

        # Determine status and activation based on original logic
        if len(cards) == 0:
            status = FileStatus.NOT_A_CARD
            activation_status = 0
        else:
            # Check if we have card numbers detected
            card_number_detected = any(d.class_id == 1 for d in detections)

            if card_number_detected:
                status = FileStatus.MASKED
                activation_status = 0
            else:
                # Simplified logic: if cards detected but no card numbers, trigger OCR
                # This replaces the original condition: len(cards) > len(card_numbers)
                status = FileStatus.UNMASKED
                activation_status = 1  # Trigger OCR

        return YoloResponse(
            filename=filename,
            img_type=file_extension,
            status=status,
            random_id=file_id,
            main_ext=file_extension,
            detections=detections,
            activation_status=activation_status,
            dest_path=None  # We work in memory, no file path
        )

    def get_detection_coordinates_for_ocr(self, detections: List[Detection]) -> List[List[float]]:
        """Get card coordinates in format expected by OCR service"""
        # Return coordinates for cards (class_id = 0) in format [x1, y1, x2, y2, class_id]
        card_coords = []
        for det in detections:
            if det.class_id == 0:  # Only cards, not card_numbers
                card_coords.append([det.x1, det.y1, det.x2, det.y2, det.class_id])

        return card_coords

    def print_results(self, results):
        """Print YOLO results for debugging"""
        results.print()
        return results.pandas().xyxy[0]  # Return as pandas DataFrame for easy viewing
