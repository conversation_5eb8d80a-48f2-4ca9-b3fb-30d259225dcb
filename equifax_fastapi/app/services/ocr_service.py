import numpy as np
import cv2
from typing import List, <PERSON><PERSON>
import gc
from paddleocr import PaddleOC<PERSON>

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import OCRResponse, FileStatus
from ..utils.helpers import clean_text, mask_ocr_detections

class OCRService:
    """OCR service using PaddleOCR for card number detection and masking"""
    
    def __init__(self):
        self.ocr = None
        self._load_ocr()
    
    def _load_ocr(self):
        """Load PaddleOCR with configuration"""
        try:
            self.ocr = PaddleOCR(
                use_angle_cls=config.ocr_use_angle_cls,
                lang=config.ocr_lang,
                show_log=config.ocr_show_log
            )
            app_logger.log_info("PaddleOCR loaded successfully")
        except Exception as e:
            app_logger.log_error("Failed to load PaddleOCR", e)
            raise
    
    def process_image_with_ocr(self, image_array: np.ndarray, card_coordinates: List[List[float]], 
                              filename: str, file_id: str, file_extension: str) -> Tuple[np.ndarray, OCRResponse]:
        """
        Process image with OCR to detect and mask card numbers
        
        Args:
            image_array: Input image as numpy array
            card_coordinates: List of card coordinates [x1, y1, x2, y2, class_id]
            filename: Original filename
            file_id: Unique file identifier
            file_extension: File extension
            
        Returns:
            Tuple of (masked_image, ocr_response)
        """
        try:
            # Convert image array to format expected by OCR
            if image_array.dtype != np.uint8:
                image_array = (image_array * 255).astype(np.uint8)
            
            # Ensure image is in BGR format for OpenCV operations
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                # Assume input is RGB, convert to BGR for OpenCV
                card_img = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            else:
                card_img = image_array.copy()
            
            # Process each card region
            masked_image, status = self._process_card_regions(card_img, card_coordinates)
            
            # Clean up memory
            gc.collect()
            
            response = OCRResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus(status),
                random_id=file_id,
                main_ext=file_extension
            )
            
            return masked_image, response
            
        except Exception as e:
            app_logger.log_error(f"Error in OCR processing for {filename}", e)
            response = OCRResponse(
                filename=filename,
                img_type=file_extension,
                status=FileStatus.INTERNAL_ERROR,
                random_id=file_id,
                main_ext=file_extension
            )
            return image_array, response
    
    def _process_card_regions(self, card_img: np.ndarray, card_coordinates: List[List[float]]) -> Tuple[np.ndarray, str]:
        """
        Process each card region with OCR and apply masking
        
        Args:
            card_img: Input image in BGR format
            card_coordinates: List of card coordinates [x1, y1, x2, y2, class_id]
            
        Returns:
            Tuple of (processed_image, status)
        """
        # Extract card regions (class_id = 0)
        card_regions = [coord[0:4] for coord in card_coordinates if int(coord[-1]) == 0]
        
        overall_status = "UNMASKED"
        
        for card_idx, card_coords in enumerate(card_regions, 1):
            app_logger.log_debug(f"Processing card region {card_idx}")
            
            # Extract card coordinates
            x1, y1, x2, y2 = map(int, card_coords)
            
            # Crop card region
            cropped_card = card_img[y1:y2, x1:x2]
            
            # Run OCR on cropped card
            ocr_result = self.ocr.ocr(cropped_card, cls=True)
            
            # Find card numbers in OCR result
            mask_areas = self._identify_card_numbers(ocr_result)
            
            if mask_areas:
                overall_status = "MASKED"
                # Mask the card numbers using helper function
                masked_cropped_card = mask_ocr_detections(cropped_card, mask_areas)
                # Put the masked card back into the original image
                card_img[y1:y2, x1:x2] = masked_cropped_card
        
        return card_img, overall_status
    
    def _identify_card_numbers(self, ocr_result: List) -> List[Tuple[int, int, int, int]]:
        """
        Identify card numbers in OCR result and return coordinates for masking
        
        Args:
            ocr_result: Raw OCR result from PaddleOCR
            
        Returns:
            List of coordinates to mask [x1, y1, x2, y2]
        """
        mask_areas = []
        
        for result_group in ocr_result:
            if result_group is not None:
                for text_line in result_group:
                    # Extract text coordinates and data
                    text_coords = (
                        int(text_line[0][0][0]), int(text_line[0][0][1]), 
                        int(text_line[0][2][0]), int(text_line[0][2][1])
                    )
                    text_data = text_line[1][0]
                    
                    # Clean the text using helper function
                    cleaned_text = clean_text(text_data)
                    
                    # Skip if it's a date pattern (MM/YY format)
                    if self._is_date_pattern(cleaned_text):
                        continue
                    
                    # Check if it's a card number (digits only, length > 3)
                    if cleaned_text.isdigit() and len(cleaned_text) > 3:
                        mask_areas.append(text_coords)
        
        return mask_areas
    
    def _is_date_pattern(self, text: str) -> bool:
        """
        Check if text matches a date pattern (MM/YY or MM/YYYY)
        
        Args:
            text: Text to check
            
        Returns:
            True if text is a date pattern, False otherwise
        """
        if '/' not in text:
            return False
            
        parts = text.split('/')
        if len(parts) != 2:
            return False
            
        month, year = parts[0], parts[1]
        
        # Check if month and year are digits
        if not (month.isdigit() and year.isdigit()):
            return False
            
        # Check if month is valid (01-12)
        if not (1 <= int(month) <= 12):
            return False
            
        # Check if year is valid (2 or 4 digits)
        if len(year) not in [2, 4]:
            return False
            
        return True
