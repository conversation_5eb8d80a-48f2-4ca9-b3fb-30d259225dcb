import io
import uuid
import base64
from typing import List, Tu<PERSON>, Optional, Union
import numpy as np
from PIL import Image
import cv2
from pdf2image import convert_from_bytes
import img2pdf
import pillow_heif
from docx import Document
from docx.shared import Inches

from ..core.config import config
from ..core.logger import app_logger
from ..models.schemas import FileValidationResult, FileConversionResult, ImageArray

# Register HEIF opener
pillow_heif.register_heif_opener()

class FileHandler:
    """Handles file validation, conversion, and result stitching - all in memory"""

    def __init__(self):
        self.valid_extensions = config.valid_extensions
        self.poppler_path = config.poppler_path
    
    def validate_file_type(self, file_extension: str) -> FileValidationResult:
        """Validate if file type is supported"""
        # Convert to lowercase for comparison
        ext_lower = file_extension.lower()

        if ext_lower in self.valid_extensions:
            return FileValidationResult(
                is_valid=True,
                file_type=ext_lower
            )
        else:
            return FileValidationResult(
                is_valid=False,
                file_type=ext_lower,
                error_message=f"Unsupported file type: {file_extension}"
            )
    
    def convert_file_to_images(self, file_data: bytes, file_extension: str) -> FileConversionResult:
        """Convert various file formats to image arrays - all in memory"""
        file_id = str(uuid.uuid1())

        try:
            ext_lower = file_extension.lower()

            if ext_lower == 'pdf':
                return self._convert_pdf_to_images_memory(file_data, file_id, ext_lower)
            elif ext_lower == 'docx':
                return self._convert_docx_to_images_memory(file_data, file_id, ext_lower)
            elif ext_lower == 'heic':
                return self._convert_heic_to_images_memory(file_data, file_id, ext_lower)
            elif ext_lower in ['jpg', 'jpeg', 'png']:
                return self._convert_image_to_array_memory(file_data, file_id, ext_lower)
            else:
                return FileConversionResult(
                    success=False,
                    file_id=file_id,
                    original_extension=ext_lower,
                    error_message=f"Unsupported file type: {file_extension}"
                )
        except Exception as e:
            app_logger.log_error(f"Error converting file {file_id}", e)
            return FileConversionResult(
                success=False,
                file_id=file_id,
                original_extension=file_extension,
                error_message=str(e)
            )
    
    def _convert_pdf_to_images_memory(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert PDF to image arrays - all in memory"""
        # Convert PDF bytes directly to images
        images = convert_from_bytes(file_data, dpi=200)

        image_arrays = []
        for num, image in enumerate(images):
            # Convert PIL image directly to numpy array
            img_array = np.array(image)
            image_arrays.append(ImageArray(
                data=img_array,
                width=img_array.shape[1],
                height=img_array.shape[0],
                channels=img_array.shape[2] if len(img_array.shape) > 2 else 1,
                page_number=num
            ))

        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )
    
    def _convert_docx_to_images_memory(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert DOCX to images - extract embedded images from memory"""
        try:
            # Load DOCX from bytes
            doc_stream = io.BytesIO(file_data)
            doc = Document(doc_stream)

            image_arrays = []

            # Extract images from document relationships
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_data = rel.target_part.blob
                    image = Image.open(io.BytesIO(image_data))
                    img_array = np.array(image)
                    image_arrays.append(ImageArray(
                        data=img_array,
                        width=img_array.shape[1],
                        height=img_array.shape[0],
                        channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
                    ))

            # If no images found, create a text-based image representation
            if not image_arrays:
                # For now, return empty - could implement text-to-image conversion later
                app_logger.log_warning(f"No images found in DOCX file {file_id}")

            return FileConversionResult(
                success=True,
                images=image_arrays,
                file_id=file_id,
                original_extension=file_extension
            )
        except Exception as e:
            return FileConversionResult(
                success=False,
                file_id=file_id,
                original_extension=file_extension,
                error_message=f"Error processing DOCX: {str(e)}"
            )
    
    def _convert_heic_to_images_memory(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert HEIC to image arrays - all in memory"""
        # Read HEIC from bytes
        heif_file = pillow_heif.read_heif(file_data)
        image = Image.frombytes(
            heif_file.mode,
            heif_file.size,
            bytes(heif_file.data),
            "raw"
        )

        # Convert to array
        img_array = np.array(image)
        image_arrays = [ImageArray(
            data=img_array,
            width=img_array.shape[1],
            height=img_array.shape[0],
            channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
        )]

        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )

    def _convert_image_to_array_memory(self, file_data: bytes, file_id: str, file_extension: str) -> FileConversionResult:
        """Convert regular image to array - all in memory"""
        # Load image from bytes
        image = Image.open(io.BytesIO(file_data))
        img_array = np.array(image)

        image_arrays = [ImageArray(
            data=img_array,
            width=img_array.shape[1],
            height=img_array.shape[0],
            channels=img_array.shape[2] if len(img_array.shape) > 2 else 1
        )]

        return FileConversionResult(
            success=True,
            images=image_arrays,
            file_id=file_id,
            original_extension=file_extension
        )

    def combine_processed_images_to_bytes(self, processed_images: List[np.ndarray],
                                         original_extension: str) -> Tuple[bool, Union[bytes, List[bytes]], str]:
        """Combine processed image arrays back to original format - return bytes for streaming"""
        try:
            ext_lower = original_extension.lower()

            if ext_lower == 'pdf':
                return self._combine_to_pdf_bytes(processed_images)
            elif ext_lower == 'heic':
                return self._combine_to_heic_bytes(processed_images[0])  # HEIC is single image
            elif ext_lower == 'docx':
                return self._combine_to_docx_bytes(processed_images)
            else:
                # For regular images, return the processed image as bytes
                return self._image_to_bytes(processed_images[0], ext_lower)
        except Exception as e:
            app_logger.log_error(f"Error combining processed images", e)
            return False, b"", str(e)

    def _combine_to_pdf_bytes(self, processed_images: List[np.ndarray]) -> Tuple[bool, bytes, str]:
        """Combine processed image arrays to PDF bytes"""
        try:
            # Convert numpy arrays to PIL Images
            pil_images = []
            for img_array in processed_images:
                if img_array.dtype != np.uint8:
                    img_array = (img_array * 255).astype(np.uint8)
                pil_image = Image.fromarray(img_array)
                pil_images.append(pil_image)

            # Convert PIL images to bytes for img2pdf
            image_bytes_list = []
            for pil_image in pil_images:
                img_bytes = io.BytesIO()
                pil_image.save(img_bytes, format='JPEG')
                image_bytes_list.append(img_bytes.getvalue())

            # Create PDF from image bytes
            pdf_bytes = img2pdf.convert(image_bytes_list)
            return True, pdf_bytes, "application/pdf"

        except Exception as e:
            return False, b"", f"Error creating PDF: {str(e)}"

    def _combine_to_heic_bytes(self, processed_image: np.ndarray) -> Tuple[bool, bytes, str]:
        """Convert processed image array to HEIC bytes"""
        try:
            if processed_image.dtype != np.uint8:
                processed_image = (processed_image * 255).astype(np.uint8)

            pil_image = Image.fromarray(processed_image)
            img_bytes = io.BytesIO()
            pil_image.save(img_bytes, format='HEIC')
            return True, img_bytes.getvalue(), "image/heic"

        except Exception as e:
            return False, b"", f"Error creating HEIC: {str(e)}"

    def _combine_to_docx_bytes(self, processed_images: List[np.ndarray]) -> Tuple[bool, List[bytes], str]:
        """Convert processed images to individual image bytes (for DOCX embedded images)"""
        try:
            image_bytes_list = []
            for img_array in processed_images:
                if img_array.dtype != np.uint8:
                    img_array = (img_array * 255).astype(np.uint8)

                pil_image = Image.fromarray(img_array)
                img_bytes = io.BytesIO()
                pil_image.save(img_bytes, format='JPEG')
                image_bytes_list.append(img_bytes.getvalue())

            return True, image_bytes_list, "application/json"  # Return as list for JSON response

        except Exception as e:
            return False, [], f"Error processing DOCX images: {str(e)}"

    def _image_to_bytes(self, processed_image: np.ndarray, original_extension: str) -> Tuple[bool, bytes, str]:
        """Convert processed image array to bytes"""
        try:
            if processed_image.dtype != np.uint8:
                processed_image = (processed_image * 255).astype(np.uint8)

            pil_image = Image.fromarray(processed_image)
            img_bytes = io.BytesIO()

            # Determine format (extension is already lowercase)
            format_map = {
                'jpg': 'JPEG',
                'jpeg': 'JPEG',
                'png': 'PNG'
            }
            format_name = format_map.get(original_extension, 'JPEG')
            content_type = f"image/{original_extension}"

            pil_image.save(img_bytes, format=format_name)
            return True, img_bytes.getvalue(), content_type

        except Exception as e:
            return False, b"", f"Error creating image: {str(e)}"

    # Note: No file cleanup needed since we work entirely in memory
