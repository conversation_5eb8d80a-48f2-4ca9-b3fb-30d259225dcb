from pydantic import BaseModel, Field
from typing import List, Optional, Union, Dict, Any
from enum import Enum

class FileStatus(str, Enum):
    """File processing status"""
    MASKED = "MASKED"
    UNMASKED = "UNMASKED"
    NOT_A_CARD = "Not a card"
    FILE_READ_ERROR = "File read error"
    INTERNAL_ERROR = "Internal error"

class APIType(str, Enum):
    """API type for logging"""
    YOLO = "yolo"
    OCR = "OCR"
    FILE_READ_ERROR = "File read error"

# Detection Models
class Detection(BaseModel):
    """Single detection result"""
    x1: float = Field(..., description="Top-left x coordinate")
    y1: float = Field(..., description="Top-left y coordinate") 
    x2: float = Field(..., description="Bottom-right x coordinate")
    y2: float = Field(..., description="Bottom-right y coordinate")
    confidence: float = Field(..., description="Detection confidence score")
    class_id: int = Field(..., description="Class ID (0=card, 1=card_number, 2=no_card)")
    class_name: str = Field(..., description="Class name")

class YoloResponse(BaseModel):
    """YOLO detection response"""
    filename: str
    img_type: str
    status: FileStatus
    random_id: str
    main_ext: str
    detections: List[Detection] = Field(default_factory=list)
    activation_status: int = Field(0, description="1 if OCR should be triggered")
    dest_path: Optional[str] = Field(None, description="Path to processed image")

class OCRResponse(BaseModel):
    """OCR processing response"""
    filename: str
    img_type: str
    status: FileStatus
    random_id: str
    main_ext: str

# File Handler Models
class FileValidationResult(BaseModel):
    """File validation result"""
    is_valid: bool
    file_type: str
    error_message: Optional[str] = None

class ImageArray(BaseModel):
    """Represents an image as array with metadata"""
    data: Any = Field(..., description="Image array data")
    width: int
    height: int
    channels: int
    page_number: Optional[int] = None

class FileConversionResult(BaseModel):
    """Result of file conversion to image arrays"""
    success: bool
    images: List[ImageArray] = Field(default_factory=list)
    file_id: str
    original_extension: str
    error_message: Optional[str] = None

# API Request/Response Models
class ProcessFileRequest(BaseModel):
    """Request model for file processing"""
    file_data: str = Field(..., description="Base64 encoded file data")
    file_type: str = Field(..., description="File extension")

class ProcessFileResponse(BaseModel):
    """Response model for file processing"""
    img_str: Union[str, List[str]] = Field(..., description="Base64 encoded result")
    img_type: str = Field(..., description="File type")
    status: FileStatus = Field(..., description="Processing status")

# Internal Service Models
class YoloServiceRequest(BaseModel):
    """Internal request for YOLO service"""
    image_path: str
    filename: str
    extension: str
    random_id: str
    main_ext: str

class OCRServiceRequest(BaseModel):
    """Internal request for OCR service"""
    image_path: str
    filename: str
    extension: str
    random_id: str
    main_ext: str
    card_coordinates: List[List[float]]

class FileHandlerRequest(BaseModel):
    """Internal request for file handler"""
    file_data: bytes
    file_type: str
    file_id: str

# Utility Models
class LogEntry(BaseModel):
    """Log entry model"""
    filename: str
    file_type: str
    is_card: str
    status: str
    api: str
    timestamp: str
    page_no: Optional[int] = None
