import base64
import uuid
from typing import Union, List, Tuple
import numpy as np
from PIL import Image
import io
import cv2

def generate_file_id() -> str:
    """Generate a unique file ID"""
    return str(uuid.uuid1())

def encode_image_to_base64(image_array: np.ndarray, format: str = 'JPEG') -> str:
    """Convert numpy image array to base64 string"""
    if image_array.dtype != np.uint8:
        image_array = (image_array * 255).astype(np.uint8)
    
    pil_image = Image.fromarray(image_array)
    img_buffer = io.BytesIO()
    pil_image.save(img_buffer, format=format)
    img_bytes = img_buffer.getvalue()
    
    return base64.b64encode(img_bytes).decode('utf-8')

def decode_base64_to_image(base64_string: str) -> np.ndarray:
    """Convert base64 string to numpy image array"""
    img_bytes = base64.b64decode(base64_string)
    pil_image = Image.open(io.BytesIO(img_bytes))
    return np.array(pil_image)

def validate_image_format(image_array: np.ndarray) -> bool:
    """Validate if image array has correct format"""
    if not isinstance(image_array, np.ndarray):
        return False
    
    if len(image_array.shape) not in [2, 3]:
        return False
    
    if len(image_array.shape) == 3 and image_array.shape[2] not in [1, 3, 4]:
        return False
    
    return True

def normalize_image_array(image_array: np.ndarray) -> np.ndarray:
    """Normalize image array to uint8 format"""
    if image_array.dtype == np.uint8:
        return image_array
    
    if image_array.max() <= 1.0:
        # Assume it's normalized to [0, 1]
        return (image_array * 255).astype(np.uint8)
    else:
        # Assume it's in [0, 255] but wrong dtype
        return image_array.astype(np.uint8)

def get_file_extension(filename: str) -> str:
    """Extract file extension from filename"""
    return filename.split('.')[-1].lower() if '.' in filename else ''

def is_supported_format(file_extension: str) -> bool:
    """Check if file format is supported"""
    from ..core.config import config
    return file_extension.lower() in config.valid_extensions

def create_error_response(message: str, status: str = "error") -> dict:
    """Create standardized error response"""
    return {
        "status": status,
        "message": message,
        "success": False
    }

def create_success_response(data: dict, message: str = "success") -> dict:
    """Create standardized success response"""
    return {
        "status": "success",
        "message": message,
        "success": True,
        "data": data
    }

# Annotation functions moved from YOLO service
def save_annotated_image(image_array: np.ndarray, detections: List) -> np.ndarray:
    """Save image with detection annotations (for debugging/visualization)"""
    annotated_image = image_array.copy()

    # Define colors for different classes
    colors = {
        0: (0, 255, 0),    # Green for cards
        1: (255, 0, 0),    # Red for card numbers
        2: (0, 0, 255)     # Blue for no_card
    }

    for detection in detections:
        # Handle both Detection objects and dictionaries
        if hasattr(detection, 'class_id'):
            class_id = detection.class_id
            x1, y1, x2, y2 = int(detection.x1), int(detection.y1), int(detection.x2), int(detection.y2)
            class_name = getattr(detection, 'class_name', f'class_{class_id}')
            confidence = getattr(detection, 'confidence', 0.0)
        elif isinstance(detection, dict):
            class_id = detection.get('class_id', 0)
            x1, y1, x2, y2 = int(detection.get('x1', 0)), int(detection.get('y1', 0)), int(detection.get('x2', 0)), int(detection.get('y2', 0))
            class_name = detection.get('class_name', f'class_{class_id}')
            confidence = detection.get('confidence', 0.0)
        else:
            continue

        color = colors.get(class_id, (255, 255, 255))

        # Draw bounding box
        cv2.rectangle(
            annotated_image,
            (x1, y1),
            (x2, y2),
            color,
            2
        )

        # Add label
        label = f"{class_name}: {confidence:.2f}"
        cv2.putText(
            annotated_image,
            label,
            (x1, y1) - 10,
            cv2.FONT_HERSHEY_SIMPLEX,
            0.5,
            color,
            1
        )

    return annotated_image

def mask_card_number_boxes(image_array: np.ndarray, detections: List) -> np.ndarray:
    """Apply masking rectangles over detections for card numbers (class_id == 1)"""
    # Ensure correct dtype for OpenCV operations
    if image_array.dtype != np.uint8:
        image_array = (image_array * 255).astype(np.uint8)

    masked = image_array.copy()
    for det in detections:
        try:
            # Handle both Detection objects and dictionaries
            if hasattr(det, 'class_id'):
                class_id = det.class_id
                x1, y1, x2, y2 = int(det.x1), int(det.y1), int(det.x2), int(det.y2)
            elif isinstance(det, dict):
                class_id = det.get('class_id', 0)
                x1, y1, x2, y2 = int(det.get('x1', 0)), int(det.get('y1', 0)), int(det.get('x2', 0)), int(det.get('y2', 0))
            else:
                continue

            if class_id == 1:  # Card number class
                # Draw filled rectangle (red in BGR to match OCR masking)
                cv2.rectangle(masked, (x1, y1), (x2, y2), (0, 0, 255), thickness=-1)
        except Exception:
            # Ignore any malformed detection entries
            continue

    return masked

def clean_text(text_data: str) -> str:
    """Clean text by removing special characters - utility function for OCR processing"""
    remove_special_characters = [',', ':', ';', '*', '_', ')', '(', '#', '-', ' ', '.']
    for char in remove_special_characters:
        text_data = text_data.replace(char, '')
    return text_data

def mask_ocr_detections(image_array: np.ndarray, mask_areas: List[Tuple[int, int, int, int]]) -> np.ndarray:
    """Mask specific areas in an image with red rectangles - for OCR results"""
    masked_image = image_array.copy()
    
    for coords in mask_areas:
        start_point = (coords[0], coords[1])
        end_point = (coords[2], coords[3])
        # Draw red rectangle to mask the detected text
        cv2.rectangle(
            masked_image, 
            start_point, 
            end_point, 
            color=(0, 0, 255),  # Red color in BGR
            thickness=-1  # Fill the rectangle
        )
    
    return masked_image
