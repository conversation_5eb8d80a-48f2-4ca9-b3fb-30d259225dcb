import base64
import cv2
import numpy as np
from typing import Dict, Any, List
from fastapi import HTTPException

from ..services.yolo_service import YoloService
from ..services.ocr_service import OCRService
from ..core.logger import app_logger

# Initialize services
yolo_service = YoloService()
ocr_service = OCRService()

def process_single_image(image_array: np.ndarray, file_id: str, file_type: str, 
                         page_num: int = None) -> tuple[np.ndarray, Dict[str, Any], Dict[str, Any]]:
    """
    Process a single image through the pipeline:
    1. Call YOLO service
    2. Call OCR service if needed
    Returns the original image array, YOLO result, and OCR result (if applicable).
    """
    # Convert image to base64 for service calls
    _, buffer = cv2.imencode('.jpg', image_array)
    image_base64 = base64.b64encode(buffer).decode('utf-8')
    
    yolo_result = {}
    ocr_result = {}

    # Step 1: Call YOLO service directly
    try:
        yolo_result = yolo_service.detect_cards(image_array, file_id, file_id, file_type)
    except Exception as e:
        app_logger.log_error(f"Error calling YOLO service in process_single_image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"YOLO service error: {str(e)}")
    
    # Step 2: If OCR is needed (activation_status == 1), call OCR service directly
    if yolo_result.activation_status == 1 and getattr(yolo_result, "det_ocr", None):
        try:
            ocr_result = ocr_service.process_image_with_ocr(
                file_data=image_base64,
                file_type=file_type,
                filename=file_id,
                random_id=yolo_result.get("random_id", file_id),
                main_ext=yolo_result.get("main_ext", file_type),
                card_coordinates=yolo_result.get("det_ocr", [])
            )
        except Exception as e:
            app_logger.log_error(f"OCR processing failed in process_single_image for page {page_num}: {str(e)}")
            # Continue without OCR result if it fails
    
    return image_array, yolo_result, ocr_result
