# Equifax FastAPI Card Redaction System

A modern, clean, and modular FastAPI application for card detection and redaction using YOLO and OCR technologies.

## Overview

This system processes various file formats (PDF, DOCX, PNG, JPG, HEIC) to detect and redact sensitive card information using:
- **YOLOv5** for card detection
- **PaddleOCR** for card number detection and masking
- **FastAPI** for modern async API endpoints
- **Streaming responses** for efficient file handling

## Architecture

```
equifax_fastapi/
├── app/
│   ├── main.py                # FastAPI entry point
│   ├── routes/                # API endpoints
│   │   ├── yolo.py            # YOLO detection routes
│   │   └── ocr.py             # OCR processing routes
│   ├── core/                  # Core configs and logging
│   │   ├── config.py          # YAML config loader
│   │   └── logger.py          # Daily rotating logs
│   ├── services/              # Business logic
│   │   ├── file_handler.py    # File processing (memory-based)
│   │   ├── yolo_service.py    # YOLO model inference
│   │   └── ocr_service.py     # PaddleOCR integration
│   ├── models/                # Pydantic schemas
│   │   └── schemas.py         # Request/response models
│   └── utils/
│       └── helpers.py         # Common utilities
├── configs/
│   └── config.yaml            # Central configuration
├── requirements.txt
└── README.md
```

## Key Features

### 🚀 Modern Architecture
- **FastAPI** with async/await support
- **Pydantic** models for request/response validation
- **Memory-based processing** - no temporary files
- **Streaming responses** for large files
- **Modular design** with clear separation of concerns

### 🔍 Detection Pipeline
1. **File Validation** - Support for PDF, DOCX, PNG, JPG, HEIC
2. **YOLO Detection** - Custom trained model for card detection
3. **OCR Trigger** - Simplified condition: if cards detected but no card numbers
4. **PaddleOCR Processing** - Detect and mask card numbers
5. **Result Combination** - Stitch processed pages back to original format

### 📊 Logging & Monitoring
- **Daily rotating logs** with TimedRotatingFileHandler
- **Structured logging** in original format:
  ```
  [2025-08-21 14:32:10] FILENAME: file1.pdf | FILETYPE: pdf | ISCARD: Yes | STATUS: MASKED | API: YOLO
  ```
- **Error tracking** and performance monitoring

## Installation

### 1. Clone and Setup
```bash
cd Projects/card_redaction/equifax_fastapi
pip install -r requirements.txt
```

### 2. Configure YOLO Weights
Update `configs/config.yaml` with your YOLO model path:
```yaml
yolo:
  weights: "path/to/your/best.pt"
  data: "path/to/your/card.yaml"
```

### 3. Run the Application
```bash
# Development
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Production
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## API Endpoints

### Main Wrapper Endpoint
```http
POST /wrapper
Content-Type: application/json

{
  "file_data": "base64_encoded_file",
  "file_type": "pdf"
}
```

**Response:**
```json
{
  "img-str": "base64_encoded_result",
  "img-type": "pdf", 
  "status": "MASKED"
}
```

### Streaming Endpoint
```http
POST /wrapper/stream
Content-Type: application/json

{
  "file_data": "base64_encoded_file",
  "file_type": "pdf"
}
```

Returns processed file as streaming response.

### Individual Service Endpoints

#### YOLO Detection
```http
POST /api/v1/yolo
POST /api/v1/yolo/stream
```

#### OCR Processing  
```http
POST /api/v1/ocr
POST /api/v1/ocr/stream
```

### Health Check
```http
GET /health
```

## Configuration

Edit `configs/config.yaml` to customize:

```yaml
debug: true

yolo:
  weights: "runs/train/exp/weights/best.pt"
  card_threshold: 0.6
  card_number_threshold: 0.4

ocr:
  use_angle_cls: true
  lang: "en"

logging:
  log_dir: "logs"
  log_file: "app.log"

api:
  host: "127.0.0.1"
  port: 8000
```

## Processing Flow

### Simplified OCR Trigger Logic
Unlike the original system that used TensorFlow + YOLO conditions, this version uses a simplified approach:

**Original:** `api2_json['STATUS'] == 'UNMASKED' AND api1_json['activationstatus'] == 1`

**New:** `yolo_response['activationstatus'] == 1` (when cards detected but no card numbers)

### Memory-Based Processing
- No temporary file creation
- Direct byte-to-byte processing
- Streaming responses for large files
- Automatic memory cleanup

## Development

### Adding New File Formats
1. Update `file_handler.py` with new conversion method
2. Add format to `valid_extensions` in config
3. Update schemas if needed

### Customizing Detection Logic
- Modify thresholds in `config.yaml`
- Update detection logic in `yolo_service.py`
- Adjust OCR parameters in `ocr_service.py`

### Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/
```

## Deployment

### Docker (Recommended)
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Systemd Service
```ini
[Unit]
Description=Equifax Card Redaction API
After=network.target

[Service]
Type=exec
User=www-data
WorkingDirectory=/path/to/equifax_fastapi
ExecStart=/usr/local/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

## Performance Considerations

- **Memory Usage**: Processes files entirely in memory
- **Concurrency**: FastAPI handles multiple requests efficiently  
- **Model Loading**: YOLO and OCR models loaded once at startup
- **Streaming**: Large files streamed to avoid memory issues

## Troubleshooting

### Common Issues
1. **YOLO model not found**: Check `weights` path in config
2. **OCR errors**: Ensure PaddleOCR dependencies installed
3. **Memory issues**: Use streaming endpoints for large files
4. **Import errors**: Verify all dependencies in requirements.txt

### Logs Location
- Application logs: `logs/app.log`
- Rotated daily with timestamp suffix
- Error details included with stack traces

## Migration from Original System

This refactored system maintains API compatibility while providing:
- ✅ Cleaner, more maintainable code
- ✅ Better error handling and logging  
- ✅ Memory-efficient processing
- ✅ Modern FastAPI features
- ✅ Simplified OCR trigger logic
- ✅ Streaming response support

The `/wrapper` endpoint maintains the same request/response format as the original system for seamless migration.
