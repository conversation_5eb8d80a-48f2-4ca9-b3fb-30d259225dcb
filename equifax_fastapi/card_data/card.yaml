# YOLOv5 🚀 by Ultralytics, GPL-3.0 license
# COCO128 dataset https://www.kaggle.com/ultralytics/coco128 (first 128 images from COCO train2017)
# Example usage: python train.py --data coco128.yaml
# parent
# ├── yolov5
# └── datasets
#     └── coco128  ← downloads here


# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
#path: ../datasets/coco128  # dataset root dir
train: card_data/images/train  # train images (relative to 'path') 3065 images
val: card_data/images/test  # val images (relative to 'path') 3065 images
test:  # test images (optional)

# Classes
nc: 3  # number of classes
names: ['card','card_number','no_card']  # class names


# Download script/URL (optional)
#download: https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip